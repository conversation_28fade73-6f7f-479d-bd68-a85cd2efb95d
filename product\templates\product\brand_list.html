{% extends "base.html" %}
{% load static %}
{% block css %}
    <link rel="stylesheet" href="{% static 'en/css/brand-list.css' %}">
{% endblock css %}

{% block body %}
        <!--=====================================
                    BANNER PART START
        =======================================-->
        <section class="inner-section single-banner" style="background: url(images/single-banner.jpg) no-repeat center;">
            <div class="container">
                <h2>brand list</h2>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">brand-list</li>
                </ol>
            </div>
        </section>
        <!--=====================================
                    BANNER PART END
        =======================================-->


        <!--=====================================
                    BRAND LIST PART START
        =======================================-->
        <section class="inner-section">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="top-filter">
                            <div class="filter-show">
                                <label class="filter-label">Show :</label>
                                <select class="form-select filter-select">
                                    <option value="1">12</option>
                                    <option value="2">24</option>
                                    <option value="3">36</option>
                                </select>
                            </div>
                            <div class="filter-short">
                                <label class="filter-label">Short by :</label>
                                <select class="form-select filter-select isotope-select">
                                    <option value="*">show all</option>
                                    <option value=".vegetables">vegetables</option>
                                    <option value=".fruits">fruits</option>
                                    <option value=".drinks">drinks</option>
                                    <option value=".fishes">fishes</option>
                                    <option value=".meats">meats</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row row-cols-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 isotope-items">
                    {% for brand in object_list %}
                        <div class="col all vegetables">
                            <div class="brand-wrap">
                                <div class="brand-media">
                                    <img src="{{brand.image.url}}" alt="brand">
                                    <div class="brand-overlay">
                                        <a href="/products/brands/{{brand.slug}}"><i class="fas fa-link"></i></a>
                                    </div>
                                </div>
                                <div class="brand-meta">
                                    <h4>{{brand}}</h4>
                                    <p>({{brand.product_count}} items)</p>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                {% if is_paginated %}
                <div class="row">
                    <div class="col-lg-12">
                        <div class="bottom-paginate">
                            <p class="page-info">Showing 12 of 60 Results</p>
                            <ul class="pagination">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.has_previous_page_number }}">
                                            <i class="fas fa-long-arrow-alt-left"></i>
                                        </a>
                                
                                    </li>
                                {% endif %}

                                {% for i in paginator.page_range %}
                                    {% if page_obj.number == i %}
                                        <li class="page-item"><a class="page-link active" href="#">{{i}}</a></li>
                                    {% elif i > page_obj.number|add:'-4' and i < page_obj.number|add:'4' %}
                                        <li class="page-item"><a class="page-link" href="?page={{i}}">{{i}}</a></li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.has_next_page_number }}">
                                            <i class="fas fa-long-arrow-alt-right"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </section>
        <!--=====================================
                    BRAND LIST PART END
        =======================================-->
{% endblock body %}