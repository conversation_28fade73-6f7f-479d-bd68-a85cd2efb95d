



/*========================================
            USER-FORM PAGE STYLE
=========================================*/
@import "../1-helpers/_mixin.scss";

.user-form-part {
    padding-top: 30px;
}

.user-form-logo {
    text-align: center;
    margin-bottom: 25px;

    img {
        width: 200px;
    }
}

.user-form-card {
    padding: 30px;
    border-radius: 8px;
    margin-bottom: 20px;
    background: var(--white);
    border: 1px solid var(--border);
}

.user-form-title {
    text-align: center;
    margin-bottom: 30px;

    h2 {
        font-size: 28px;
        line-height: 36px;
        margin-bottom: 5px;
        color: var(--primary);
    }

    p {
        text-transform: capitalize;
    }
}

// user form group 
.user-form-group {
    @include flex__start;
}

// user form social
.user-form-social {
    width: 100%;

    li {
        margin-bottom: 20px;

        a {
            width: 100%;
            font-size: 15px;
            padding: 10px 0px;
            border-radius: 8px;
            letter-spacing: 0.3px;
            text-align: center;
            text-transform: capitalize;
            color: var(--white);
            background: var(--primary);

            i {
                font-size: 16px;
                margin-right: 10px;
            }
        }

        &:last-child {
            margin-bottom: 0px;
        }
    }

    .facebook {
        background: var(--facebook);
    }

    .twitter {
        background: var(--twitter);
    }

    .google {
        background: var(--google);
    }

    .instagram {
        background: var(--instagram);
    }
}

.user-form-divider {
    width: 1px;
    height: 240px;
    margin: 0px 50px;
    background: var(--border);
    position: relative;
    z-index: 1;

    p {
        width: 40px;
        height: 40px;
        font-size: 14px;
        font-weight: 500;
        margin: 0 auto;
        font-style: italic;
        line-height: 38px;
        border-radius: 50%;
        text-align: center;
        color: var(--gray);
        background: var(--white);
        border: 1px solid var(--border);
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 2;
        transform: translate(-50%, -50%);
    }
}

.user-form {
    width: 100%;

    .form-group {
        margin-bottom: 20px;
    }
}

.form-button {
    text-align: center;

    button {
        width: 100%;
        height: 45px;
        font-size: 14px;
        font-weight: 500;
        line-height: 45px;
        border-radius: 8px;
        letter-spacing: 0.3px;
        text-align: center;
        text-transform: uppercase;
        color: var(--white);
        background: var(--primary);
    }

    p {
        font-size: 15px;
        margin-top: 12px;
        text-transform: capitalize;

        a {
            font-weight: 500;
            margin-left: 5px;
            color: var(--primary);

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

// user form reminder
.user-form-remind {
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    background: var(--white);
    border: 1px solid var(--border);

    p {
        font-size: 15px;
        text-transform: capitalize;

        a {
            font-weight: 500;
            margin-left: 5px;
            color: var(--primary);

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

// user form footer
.user-form-footer {
    text-align: center;
    margin-top: 25px;
    margin-bottom: 40px;

    p {
        font-size: 14px;
        color: var(--gray);

        a {
            color: var(--primary);
            @include cursor__transition;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

// responsive style
@media (max-width: 575px) {
    .user-form-card {
        padding: 20px;
    }
}

@media (max-width: 767px) {
    .user-form-group {
        display: inherit;
    }

    .user-form-divider {
        width: 100%;
        height: 1px;
        margin: 50px 0px;
    }
}