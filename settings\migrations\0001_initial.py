# Generated by Django 5.1.2 on 2024-10-18 16:50

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('logo', models.ImageField(upload_to='company')),
                ('subtitle', models.TextField(max_length=500)),
                ('facebook_link', models.URLField(blank=True, null=True)),
                ('twitter_link', models.URLField(blank=True, null=True)),
                ('youtube_link', models.URLField(blank=True, null=True)),
                ('phones', models.TextField(blank=True, max_length=500, null=True)),
                ('email', models.TextField(blank=True, max_length=500, null=True)),
                ('address', models.TextField(blank=True, max_length=500, null=True)),
                ('android_app', models.URLField(blank=True, null=True)),
                ('ios_app', models.URLField(blank=True, null=True)),
                ('call_us', models.TextField(blank=True, max_length=100, null=True)),
                ('email_us', models.TextField(blank=True, max_length=100, null=True)),
            ],
        ),
    ]
