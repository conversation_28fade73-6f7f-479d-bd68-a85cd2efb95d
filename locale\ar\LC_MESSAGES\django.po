# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
# 
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-01 17:12+0200\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: .\product\models.py:15 .\product\models.py:52
msgid "Name"
msgstr "الاسم"

#: .\product\models.py:16
msgid "Flag"
msgstr "العلامه"

#: .\product\models.py:17 .\product\models.py:45 .\product\models.py:53
msgid "Image"
msgstr "الصوره"

#: .\product\models.py:18
msgid "Price"
msgstr "السعر"

#: .\product\models.py:19
msgid "Sku"
msgstr "الكود"

#: .\product\models.py:20
msgid "Subtitle"
msgstr "عنوان"

#: .\product\models.py:21
msgid "Description"
msgstr "وصف"

#: .\product\models.py:22
msgid "Quantity"
msgstr "الكميه"

#: .\product\models.py:23
msgid "Brand"
msgstr "الماركه"

#: .\product\models.py:44 .\product\models.py:68
msgid "Product"
msgstr "المنتج"

#: .\product\models.py:67
msgid "User"
msgstr "المستخدم"

#: .\product\models.py:69
msgid "Rate"
msgstr "المستوى"

#: .\product\models.py:70
msgid "Review"
msgstr "التقييم"

#: .\product\models.py:71
msgid "Created at"
msgstr "تاريخ الانشاء"

#: .\templates\base.html:162
msgid "Home"
msgstr "الصفحه الرئيسيه"

#: .\templates\base.html:165
msgid "Shop"
msgstr "تسوق"

#: .\templates\base.html:168
#, fuzzy
#| msgid "Brand"
msgid "Brands"
msgstr "الماركه"

#: .\templates\base.html:171
msgid "Orders"
msgstr "الطلبات"
