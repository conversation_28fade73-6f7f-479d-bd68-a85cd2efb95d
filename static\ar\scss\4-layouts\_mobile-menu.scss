


//========================================
//        MOBILE MENU LAYOUT STYLE
//========================================
.mobile-menu {
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    z-index: 3;
    background: var(--white);
    border-radius: 10px 10px 0px 0px;
    box-shadow: 0px -5px 15px 0px rgba(0, 0, 0, 0.1);
    @include flex__center;
    justify-content: space-between;
    display: none;

    a,
    button {
        @include flex__center;
        flex-direction: column;
        width: 80px;
        padding: 8px 0px;
        border-radius: 8px;
        position: relative;

        &:hover {
            background: var(--chalk);

            i {
                color: var(--primary);
            }

            span {
                color: var(--primary);
            }
        }

        i {
            font-size: 15px;
            margin-bottom: 5px;
            color: var(--text);
            text-shadow: 2px 3px 8px rgba(0, 0, 0, 0.1);
        }

        span {
            font-size: 10px;
            line-height: 12px;
            color: var(--text);
            text-transform: uppercase;
        }

        sup {
            position: absolute;
            top: -5px;
            left: 75%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            line-height: 20px;
            border-radius: 50%;
            text-align: center;
            transform: translateX(-50%);
            color: var(--white);
            background: var(--primary);
            border: 2px solid var(--green-chalk);
            text-shadow: var(--primary-tshadow);
        }
    
        .fas fa-shopping-basket {
            font-size: 18px;
        }
    }
}

@media (max-width: 991px) {
    .mobile-menu {
        display: flex;
    }
}
