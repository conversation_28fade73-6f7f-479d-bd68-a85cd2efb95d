


/*========================================
            ABOUT PAGE STYLE
=========================================*/
@import "../1-helpers/_mixin.scss";

body {
    background: var(--white);
}

.about-content {
    h2 {
        margin-bottom: 25px;
    }

    p {
        margin-bottom: 40px;
    }
}

.about-list {
    @include flex__center__between;

    li {
        padding-right: 30px;
        border-right: 1px solid var(--border);

        &:first-child {
            border-right: 0px;
            padding-right: 0px;
        }

        h3 {
            margin-bottom: 5px;
        }
    
        h6 {
            font-weight: 400;
            color: var(--text);
            text-transform: capitalize;
        }
    }
}

.about-img {
    display: grid;
    grid-gap: 20px;
    grid-template-columns: 1fr 1fr;
    margin-right: 50px;

    img {
        width: 100%;
        border-radius: 8px;
    }
}


// TESTIMONIAL PART
.about-testimonial {
    background: url(../../images/testimonial-shape.png);
    @include background__property;
    padding: 100px 0px 70px;
}

.testi-content {
    @include flex__center;
    padding-bottom: 30px;
}

.testi-img {
    margin-left: 80px;
    border-radius: 50%;
    box-shadow: var(--primary-bshadow);

    img {
        width: 300px;
        height: 300px;
        border-radius: 50%;
        border: 10px solid var(--white);
    }
}

.testi-quote {
    width: 600px;

    i {
        font-size: 50px;
        color: var(--primary);
        display: inline-block;
        margin-bottom: 30px;
    }

    p {
        font-size: 24px;
        line-height: 38px;
        margin-bottom: 30px;
    }

    h4 {
        text-transform: capitalize;
        margin-bottom: 5px;
    }

    h6 {
        font-weight: 400;
        color: var(--text);
    }
}

// CHOOSE PART
.about-choose {
    margin-bottom: 75px;
}

.choose-card {
    margin: 25px 15px;
}

// BRAND PART
.about-brand {
    background: var(--green-chalk);
    padding: 90px 0px 100px;
}

.brand-slider {
    li {
        margin: 0px 10px;
        padding: 20px 0px;
        border-radius: 8px;
        background: var(--white);

        img {
            width: 100%;
        }
    }
}

// TEAM PART
.team-slider {
    li {
        margin: 0px 12px;
    }

    .dandik,
    .bamdik {
        top: 40%;
    }
}

//========================================
//         RESPONSIVE ABOUT STYLE
//========================================
@media (max-width: 575px) {
    .about-list {
        li {
            padding-right: 15px;
        }
    }
}

@media (max-width: 767px) {
    .testi-content {
        flex-direction: column;
    }

    .testi-img {
        margin-left: 0px;
    }

    .testi-quote {
        width: 100%;
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .testi-img {
        margin-left: 50px;
    }

    .testi-quote {
        width: 100%;
    }
}

@media (max-width: 991px) {
    .about-list {
        margin-bottom: 30px;
    }

    .about-img {
        margin-right: 0px;
    }
}

