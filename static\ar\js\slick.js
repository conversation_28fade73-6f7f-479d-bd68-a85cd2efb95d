


//========================================
//        HOME INDEX BANNER SLIDER
//========================================
$('.home-index-slider').slick({
  dots: false,
  fade: true,
  infinite: true,
  autoplay: true,
  arrows: true,
  rtl: true,
  speed: 600,
  prevArrow: '<i class="icofont-arrow-right dandik"></i>',
  nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
  slidesToShow: 1,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 992,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 576,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
      }
    }
  ]
});


//========================================
//         HOME GRID BANNER SLIDER
//========================================
$('.home-grid-slider').slick({
  dots: true,
  fade: false,
  infinite: true,
  autoplay: true,
  arrows: true,
  rtl: true,
  speed: 600,
  prevArrow: '<i class="icofont-arrow-right dandik"></i>',
  nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
  slidesToShow: 1,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 992,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 576,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
      }
    }
  ]
});


//========================================
//       HOME CATEGORY BANNER SLIDER
//========================================
$('.home-category-slider').slick({
    dots: true,
    fade: true,
    infinite: true,
    autoplay: true,
    arrows: true,
    rtl: true,
    speed: 500,
    prevArrow: '<i class="icofont-arrow-right dandik"></i>',
    nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
    slidesToShow: 1,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          arrows: false,
        }
      }
    ]
  });


//========================================
//       HOME CLASSIC BANNER SLIDER
//========================================
$('.home-classic-slider').slick({
  dots: false,
  fade: false,
  infinite: true,
  autoplay: true,
  arrows: true,
  rtl: true,
  speed: 800,
  prevArrow: '<i class="icofont-arrow-right dandik"></i>',
  nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
  slidesToShow: 1,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 992,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 576,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
      }
    }
  ]
});


//========================================
//      FOR SUGGEST CATEGORY SLIDER
//========================================
  $('.suggest-slider').slick({
    dots: false,
    infinite: true,
    autoplay: true,
    arrows: true,
    rtl: true,
    speed: 1000,
    prevArrow: '<i class="icofont-arrow-right dandik"></i>',
    nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
    slidesToShow: 5,
    slidesToScroll: 2,
    responsive: [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 4,
        }
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        }
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
          arrows: false,
        }
      }
    ]
  });


//========================================
//        FOR NEW PRODUCT SLIDER
//========================================
    $('.new-slider').slick({
      dots: false,
      infinite: true,
      autoplay: true,
      arrows: true,
      rtl: true,
      speed: 800,
      prevArrow: '<i class="icofont-arrow-right dandik"></i>',
      nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
      slidesToShow: 5,
      slidesToScroll: 1,
      responsive: [
        {
          breakpoint: 1200,
          settings: {
            slidesToShow: 4,
            slidesToScroll: 2,
          }
        },
        {
          breakpoint: 992,
          settings: {
            slidesToShow: 3,
            slidesToScroll: 3,
          }
        },
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 2,
            slidesToScroll: 2,
          }
        },
        {
          breakpoint: 576,
          settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            variableWidth: true,
            arrows: false,
          }
        }
      ]
    });


//========================================
//          FOR CATEGORY SLIDER
//========================================
$('.category-slider').slick({
    dots: false,
    infinite: true,
    autoplay: true,
    arrows: true,
    rtl: true,
    speed: 800,
    prevArrow: '<i class="icofont-arrow-right dandik"></i>',
    nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
    slidesToShow: 5,
    slidesToScroll: 1,
    responsive: [
            {
            breakpoint: 1200,
            settings: {
                slidesToShow: 5,
                slidesToScroll: 5,
            }
        },
            {
            breakpoint: 992,
            settings: {
                slidesToShow: 4,
                slidesToScroll: 4,
            }
        },
            {
            breakpoint: 768,
            settings: {
                slidesToShow: 3,
                slidesToScroll: 3,
            }
        },
            {
            breakpoint: 576,
            settings: {
                slidesToShow: 2,
                slidesToScroll: 2,
                variableWidth: true,
                arrows: false,
            }
        }
    ]
});


//========================================
//            BRAND SLIDER
//========================================
$('.brand-slider').slick({
  dots: false,
  infinite: true,
  autoplay: false,
  arrows: true,
  rtl: true,
  speed: 800,
  prevArrow: '<i class="icofont-arrow-right dandik"></i>',
  nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
  slidesToShow: 5,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 4,
      }
    },
    {
      breakpoint: 992,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 3,
      }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2,
      }
    },
    {
      breakpoint: 576,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
        variableWidth: true,
        arrows: false,
      }
    }
  ]
  });


//========================================
//            BLOG CARD SLIDER
//========================================
$('.blog-slider').slick({
  dots: false,
  infinite: true,
  autoplay: false,
  arrows: true,
  rtl: true,
  speed: 800,
  prevArrow: '<i class="icofont-arrow-right dandik"></i>',
  nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
  slidesToShow: 3,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 3,
      }
    },
    {
      breakpoint: 992,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2,
      }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    },
    {
      breakpoint: 576,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
      }
    }
  ]
  });


//========================================
//        TESTIMONIAL SLIDER
//========================================
$('.testimonial-slider').slick({
  dots: false,
  infinite: true,
  autoplay: false,
  arrows: true,
  fade: false,
  rtl: true,
  speed: 1000,
  centerMode: true,
  centerPadding: '250px',
  slidesToShow: 1,
  slidesToScroll: 1,
  prevArrow: '<i class="icofont-arrow-right dandik"></i>',
  nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
  responsive: [
    {
        breakpoint: 1200,
        settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            centerPadding: '250px',
        }
    },
    {
        breakpoint: 992,
        settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            centerPadding: '130px',
        }
    },
    {
        breakpoint: 768,
        settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            centerPadding: '40px',
        }
    },
    {
        breakpoint: 576,
        settings: {
            arrows: false,
            slidesToShow: 1,
            slidesToScroll: 1,
            centerPadding: '10px',
        }
    }
]
});


$('.testi-slider').slick({
dots: false,
infinite: true,
autoplay: false,
arrows: true,
rtl: true,
speed: 800,
prevArrow: '<i class="icofont-arrow-right dandik"></i>',
nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
slidesToShow: 1,
slidesToScroll: 1,
responsive: [
  {
    breakpoint: 1200,
    settings: {
      slidesToShow: 1,
      slidesToScroll: 1,
    }
  },
  {
    breakpoint: 992,
    settings: {
      slidesToShow: 1,
      slidesToScroll: 1,
    }
  },
  {
    breakpoint: 768,
    settings: {
      slidesToShow: 1,
      slidesToScroll: 1,
    }
  },
  {
    breakpoint: 576,
    settings: {
      slidesToShow: 1,
      slidesToScroll: 1,
    }
  }
]
});


//========================================
//            TEAM SLIDER
//========================================
$('.team-slider').slick({
  dots: false,
  infinite: true,
  autoplay: false,
  arrows: true,
  rtl: true,
  speed: 800,
  prevArrow: '<i class="icofont-arrow-right dandik"></i>',
  nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
  slidesToShow: 4,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 3,
      }
    },
    {
      breakpoint: 992,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 3,
      }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2,
      }
    },
    {
      breakpoint: 576,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
    }
  ]
  });


//========================================
//  FOR PRODUCT POPUP VIEW IMAGE SLIDER
//========================================
$('.preview-slider').slick({
  slidesToShow: 1,
  slidesToScroll: 1,
  arrows: true,
  fade: true,
  rtl: true,
  asNavFor: '.thumb-slider',
  prevArrow: '<i class="icofont-arrow-right dandik"></i>',
  nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
  responsive: [
      {
          breakpoint: 576,
          settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: true,
          }
      }
  ]
});

$('.thumb-slider').slick({
  slidesToShow: 3,
  slidesToScroll: 1,
  asNavFor: '.preview-slider',
  rtl: true,
  dots: false,
  arrows: false,
  centerMode: true,
  focusOnSelect: true,
  responsive: [
      {
          breakpoint: 992,
          settings: {
            slidesToShow: 3,
            slidesToScroll: 1,
          }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 400,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
    }
  ]
});


//========================================
//   FOR PRODUCT DETAILS IMAGE SLIDER
//========================================
$('.details-preview').slick({
  slidesToShow: 1,
  slidesToScroll: 1,
  arrows: false,
  fade: true,
  rtl: true,
  asNavFor: '.details-thumb',
  prevArrow: '<i class="icofont-arrow-right dandik"></i>',
  nextArrow: '<i class="icofont-arrow-left bamdik"></i>',
  responsive: [
      {
          breakpoint: 576,
          settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: true,
          }
      }
  ]
});

$('.details-thumb').slick({
  slidesToShow: 5,
  slidesToScroll: 1,
  asNavFor: '.details-preview',
  rtl: true,
  dots: false,
  arrows: false,
  focusOnSelect: true,
  responsive: [
        {
          breakpoint: 1200,
          settings: {
            slidesToShow: 3,
            slidesToScroll: 1,
          }
      },
      {
          breakpoint: 992,
          settings: {
            slidesToShow: 5,
            slidesToScroll: 1,
          }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 1,
          vertical: false,
        }
      },
      {
        breakpoint: 400,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
          vertical: false,
        }
    }
  ]
});


