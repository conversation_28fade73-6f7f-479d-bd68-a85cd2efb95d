{% extends "base.html" %}
{% load static %}
{% block css %}
    <link rel="stylesheet" href="{% static 'en/css/product-details.css' %}">
{% endblock css %}

{% block body %}

        <!--=====================================
                    BANNER PART START
        =======================================-->
        <section class="single-banner inner-section" style="background: url(images/single-banner.jpg) no-repeat center;">
            <div class="container">
                <h2>product simple</h2>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                    <li class="breadcrumb-item"><a href="shop-4column.html">shop-4column</a></li>
                    <li class="breadcrumb-item active" aria-current="page">product-simple</li>
                </ol>
            </div>
        </section>
        <!--=====================================
                    BANNER PART END
        =======================================-->


        <!--=====================================
                PRODUCT DETAILS PART START
        =======================================-->
        <section class="inner-section">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="details-gallery">
                            <div class="details-label-group">
                                <label class="details-label new">{{object.flag}}</label>
                            </div>
                            <ul class="details-preview"> 
                                {% for image in object.product_image.all %}
                                    <li><img src="{{image.image.url}}" alt="product"></li>
                                {% endfor %}
                            </ul>
                            <ul class="details-thumb">
                                {% for image in object.product_image.all %}
                                    <li><img src="{{image.image.url}}" alt="product"></li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="details-content">
                            <h3 class="details-name"><a href="#">{{object}}</a></h3>
                            <div class="details-meta">
                                <p>SKU:<span>{{object.sku}}</span></p>
                                <p>BRAND:<a href="#">{{object.brand}}</a></p>
                            </div>
                            <div class="details-rating">
                                <i class="active icofont-star"></i>
                                <i class="active icofont-star"></i>
                                <i class="active icofont-star"></i>
                                <i class="active icofont-star"></i>
                                <i class="icofont-star"></i>
                                <a href="#">(3 reviews)</a>
                            </div>
                            <h3 class="details-price">
                                <span>${{object.price}}</span>
                            </h3>
                            <p class="details-desc">{{object.subtitle}}</p>
                            <div class="details-list-group">
                                <label class="details-list-title">tags:</label>
                                <ul class="details-tag-list">
                                    {% for tag in object.tags.all %}
                                        <li><a href="#">{{tag}}</a></li>
                                    {% endfor %}
                                </ul>
                            </div>
                            <div class="details-list-group">
                                <label class="details-list-title">Share:</label>
                                <ul class="details-share-list">
                                    <li><a href="#" class="icofont-facebook" title="Facebook"></a></li>
                                    <li><a href="#" class="icofont-twitter" title="Twitter"></a></li>
                                    <li><a href="#" class="icofont-linkedin" title="Linkedin"></a></li>
                                    <li><a href="#" class="icofont-instagram" title="Instagram"></a></li>
                                </ul>
                            </div>
                            <div class="details-add-group">
                                <form action="{% url 'orders:add_to_cart' %}" method="post">
                                    {% csrf_token %}
                                    <input class="" title="Quantity Number" type="text" name="quantity" value="1">
                                    <input type="hidden" name="product_id" value="{{object.id}}">
                                    <button type="submit" class="product-add" title="Add to Cart">
                                        <i class="fas fa-shopping-basket"></i>
                                        <span>add to cart</span>
                                    </button>
                                 </form>
                            </div>
                            <div class="details-action-group">
                                <a class="details-wish wish" href="#" title="Add Your Wishlist">
                                    <i class="icofont-heart"></i>
                                    <span>add to wish</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--=====================================
                PRODUCT DETAILS PART END
        =======================================-->


        <!--=====================================
                  PRODUCT TAB PART START
        =======================================-->
        <section class="inner-section">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="product-details-frame">
                            <h3 class="frame-title">Description</h3>
                            <div class="tab-descrip">
                                <p>{{object.description}}</p>
                            </div>
                        </div>
                        <div class="product-details-frame">
                            <h3 class="frame-title">Reviews ({{reviews |length}})</h3>
                            <ul class="review-list">
                                {% for review in reviews %}
                                    <li class="review-item">
                                        <div class="review-media">
                                            <a class="review-avatar" href="#">
                                                <img src="images/avatar/01.jpg" alt="review">
                                            </a>
                                            <h5 class="review-meta">
                                                <a href="#">{{review.user}}</a>
                                                <span>{{review.created_at | date:"F d, o"}}</span>
                                            </h5>
                                        </div>
                                        <ul class="review-rating">
                                            <li class="icofont-ui-{% if review.rate > 0 %}rating{% else %}rate-blank{% endif %}"></li>
                                            <li class="icofont-ui-{% if review.rate > 1 %}rating{% else %}rate-blank{% endif %}"></li>
                                            <li class="icofont-ui-{% if review.rate > 2 %}rating{% else %}rate-blank{% endif %}"></li>
                                            <li class="icofont-ui-{% if review.rate > 3 %}rating{% else %}rate-blank{% endif %}"></li>
                                            <li class="icofont-ui-{% if review.rate > 4 %}rating{% else %}rate-blank{% endif %}"></li>
                                        </ul>
                                        <p class="review-desc">{{review.review}}</p>
                                        </form>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="product-details-frame">
                            <h3 class="frame-title">add your review</h3>
                            <form class="review-form">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="star-rating">
                                            <input type="radio" name="rating" id="star-1"><label for="star-1"></label>
                                            <input type="radio" name="rating" id="star-2"><label for="star-2"></label>
                                            <input type="radio" name="rating" id="star-3"><label for="star-3"></label>
                                            <input type="radio" name="rating" id="star-4"><label for="star-4"></label>
                                            <input type="radio" name="rating" id="star-5"><label for="star-5"></label>
                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="form-group">
                                            <textarea class="form-control" placeholder="Describe"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <input type="text" class="form-control" placeholder="Name">
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <input type="email" class="form-control" placeholder="Email">
                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <button class="btn btn-inline">
                                            <i class="icofont-water-drop"></i>
                                            <span>drop your review</span>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--=====================================
                    PRODUCT TAB PART END
        =======================================-->


        <!--=====================================
                 PRODUCT RELATED PART START
        =======================================-->
        <section class="inner-section">
            <div class="container">
                <div class="row">
                    <div class="col">
                        <div class="section-heading">
                            <h2>related this items</h2>
                        </div>
                    </div>
                </div>
                <div class="row row-cols-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5">
                    {% for item in related_products %}
                        <div class="col">
                            <div class="product-card {% if item.quantity == 0 %}product-disable{% endif %}">
                                <div class="product-media">
                                    <div class="product-label">
                                        <label class="label-text sale">{{item.flag}}</label>
                                    </div>
                                    <button class="product-wish wish">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                    <a class="product-image" href="product-video.html">
                                        <img src="{{item.image.url}}" alt="product">
                                    </a>
                                    <div class="product-widget">
                                        <a title="Product Compare" href="compare.html" class="fas fa-random"></a>
                                        <a title="Product Video" href="https://youtu.be/9xzcVxSBbG8" class="venobox fas fa-play" data-autoplay="true" data-vbtype="video"></a>
                                        <a title="Product View" href="#" class="fas fa-eye" data-bs-toggle="modal" data-bs-target="#product-view"></a>
                                    </div>
                                </div>
                                <div class="product-content">
                                    <div class="product-rating">
                                        <i class="active icofont-star"></i>
                                        <i class="active icofont-star"></i>
                                        <i class="active icofont-star"></i>
                                        <i class="active icofont-star"></i>
                                        <i class="icofont-star"></i>
                                        <a href="product-video.html">(3)</a>
                                    </div>
                                    <h6 class="product-name">
                                        <a href="product-video.html">{{item.name}}</a>
                                    </h6>
                                    <h6 class="product-price">
                                        <span>${{item.price}}</span>
                                    </h6>
                                    <button class="product-add" title="Add to Cart">
                                        <i class="fas fa-shopping-basket"></i>
                                        <span>add</span>
                                    </button>
                                    <div class="product-action">
                                        <button class="action-minus" title="Quantity Minus"><i class="icofont-minus"></i></button>
                                        <input class="action-input" title="Quantity Number" type="text" name="quantity" value="1">
                                        <button class="action-plus" title="Quantity Plus"><i class="icofont-plus"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}

                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="section-btn-25">
                            <a href="shop-4column.html" class="btn btn-outline">
                                <i class="fas fa-eye"></i>
                                <span>view all related</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--=====================================
                 PRODUCT RELATED PART END
        =======================================-->


        <!--=====================================
                    NEWSLETTER PART START
        =======================================-->
        <section class="news-part" style="background: url(images/newsletter.jpg) no-repeat center;">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-5 col-lg-6 col-xl-7">
                        <div class="news-text">
                            <h2>Get 20% Discount for Subscriber</h2>
                            <p>Lorem ipsum dolor consectetur adipisicing accusantium</p>
                        </div>
                    </div>
                    <div class="col-md-7 col-lg-6 col-xl-5">
                        <form class="news-form">
                            <input type="text" placeholder="Enter Your Email Address">
                            <button><span><i class="icofont-ui-email"></i>Subscribe</span></button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
        <!--=====================================
                    NEWSLETTER PART END
        =======================================-->
{% endblock body %}